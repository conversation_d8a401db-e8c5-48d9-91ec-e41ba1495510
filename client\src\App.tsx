import { Routes, Route } from 'react-router-dom'
import { Navbar } from './components/Navbar'
import { Dashboard } from './pages/Dashboard'
import { TickerDetail } from './pages/TickerDetail'
import { Discovery } from './pages/Discovery'
import { Logs } from './pages/Logs'
import { Toaster } from './components/Toaster'

function App() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      <main className="container mx-auto px-4 py-8">
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/ticker/:ticker" element={<TickerDetail />} />
          <Route path="/discovery" element={<Discovery />} />
          <Route path="/logs" element={<Logs />} />
        </Routes>
      </main>
      <Toaster />
    </div>
  )
}

export default App 